package com.nacos.controller.media;

import com.nacos.entity.dto.VideoTranslateRequestDTO;
import com.nacos.entity.vo.VideoTranslateStatusVO;
import com.nacos.result.Result;
import com.nacos.service.VideoTranslateService;
import com.nacos.service.UniversalTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.MediaType;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

import java.util.Map;

/**
 * 视频翻译控制器
 *
 * 已迁移到统一任务系统架构，使用UniversalTaskService处理任务
 * 保持API接口兼容性，内部调用统一任务服务
 */
@Slf4j
@Tag(name = "视频翻译")
@RestController
@RequestMapping("/api/v1/media/video/translate")
@Validated
@RequiredArgsConstructor
public class VideoTranslateController {

    private final VideoTranslateService videoTranslateService;
    private final UniversalTaskService universalTaskService;

    /**
     * 提交视频翻译任务
     * URL: /api/v1/media/video/translate/submit
     *
     * @param request 翻译请求参数（通过form-data字段自动绑定，包含文件）
     * @return 任务提交结果
     */
    @Operation(summary = "提交视频翻译任务", description = "提交视频文件进行语言翻译处理")
    @PostMapping(value = "/submit", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result<Map<String, Object>> submitTranslateTask(
            @ModelAttribute @Valid VideoTranslateRequestDTO request) {
        String methodName = "submitTranslateTask";
        try {
            // 1. 基础参数校验（DTO的@Valid注解已处理@NotBlank、@NotNull等基础校验）
            // 额外的文件内容检查
            if (request.getFile().isEmpty()) {
                log.warn("[{}] 文件验证失败: 视频文件内容为空, userId={}", methodName, request.getUserId());
                return Result.ERROR("视频文件内容不能为空");
            }

            // 2. 调用服务层提交任务（业务校验在服务层处理）
            Result<Map<String, Object>> result = videoTranslateService.submitTranslateTask(request, request.getFile());

            if (result.isSuccess()) {
                log.info("[{}] 视频翻译任务提交成功: userId={}, taskId={}",
                        methodName, request.getUserId(), result.getData().get("taskId"));
            } else {
                log.error("[{}] 视频翻译任务提交失败: userId={}, error={}",
                        methodName, request.getUserId(), result.getMessage());
            }

            return result;

        } catch (Exception e) {
            log.error("[{}] 提交视频翻译任务异常: userId={}, error={}", methodName, request.getUserId(), e.getMessage(), e);
            return Result.ERROR("提交任务失败: " + e.getMessage());
        }
    }

    /**
     * 查询任务状态
     * URL: /api/v1/media/video/translate/status/{taskId}
     * 
     * @param taskId 任务ID
     * @return 任务状态信息
     */
    @Operation(summary = "查询任务状态", description = "根据任务ID查询视频翻译任务的当前状态和进度")
    @GetMapping("/status/{taskId}")
    public Result<VideoTranslateStatusVO> getTaskStatus(
            @Parameter(description = "任务ID") @PathVariable @NotBlank String taskId) {
        String methodName = "getTaskStatus";
        try {
            log.info("[{}] 查询任务状态: taskId={}", methodName, taskId);

            // 调用服务层查询状态
            Result<VideoTranslateStatusVO> result = videoTranslateService.getTaskStatus(taskId);

            if (result.isSuccess()) {
                log.info("[{}] 查询任务状态成功: taskId={}, status={}", methodName, taskId, result.getData().getStatus());
            } else {
                log.error("[{}] 查询任务状态失败: taskId={}, error={}", methodName, taskId, result.getMessage());
            }

            return result;

        } catch (Exception e) {
            log.error("[{}] 查询任务状态异常: taskId={}, error={}", methodName, taskId, e.getMessage(), e);
            return Result.ERROR("查询任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 查询视频翻译任务列表
     * URL: /api/v1/media/video/translate/tasks
     *
     * @param userId 用户ID（必填）
     * @param status 任务状态筛选（可选：success,processing,failed,cancelled，支持组合）
     * @param page 页码，默认1
     * @param size 每页大小，默认10，最大100
     * @param sortBy 排序字段，默认createdTime
     * @param sortOrder 排序方向，默认DESC
     * @return 分页任务列表
     */
    @Operation(summary = "查询视频翻译任务列表",
               description = "分页查询用户的视频翻译任务，支持状态筛选。status参数支持：success(成功)、processing(进行中)、failed(失败)、cancelled(已取消)，多个状态用逗号分隔")
    @GetMapping("/tasks")
    public Result<Map<String, Object>> getTaskList(
            @Parameter(description = "用户ID", required = true)
            @RequestParam("userId") @NotBlank String userId,

            @Parameter(description = "任务状态筛选", example = "success,processing")
            @RequestParam(value = "status", required = false) String status,

            @Parameter(description = "页码，从1开始", example = "1")
            @RequestParam(value = "page", defaultValue = "1") @Min(1) Integer page,

            @Parameter(description = "每页大小，最大100", example = "10")
            @RequestParam(value = "size", defaultValue = "10") @Min(1) @Max(100) Integer size,

            @Parameter(description = "排序字段", example = "createdTime")
            @RequestParam(value = "sortBy", defaultValue = "createdTime") String sortBy,

            @Parameter(description = "排序方向", example = "DESC")
            @RequestParam(value = "sortOrder", defaultValue = "DESC") String sortOrder) {

        String methodName = "getTaskList";
        try {
            log.info("[{}] 查询视频翻译任务列表: userId={}, status={}, page={}, size={}",
                    methodName, userId, status, page, size);

            // 调用服务层方法
            Result<Map<String, Object>> result = videoTranslateService.queryTaskList(
                    userId, status, page, size, sortBy, sortOrder);

            if (result.isSuccess()) {
                Map<String, Object> data = result.getData();
                log.info("[{}] 查询任务列表成功: userId={}, 共{}条记录",
                        methodName, userId, data.get("total"));
            } else {
                log.error("[{}] 查询任务列表失败: userId={}, error={}",
                        methodName, userId, result.getMessage());
            }

            return result;

        } catch (Exception e) {
            log.error("[{}] 查询视频翻译任务列表异常: userId={}, error={}",
                    methodName, userId, e.getMessage(), e);
            return Result.ERROR("查询任务列表失败: " + e.getMessage());
        }
    }
}
